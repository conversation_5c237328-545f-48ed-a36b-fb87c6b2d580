{"name": "vocab-backend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "npx tsc --noEmit --skipLibCheck --project .", "p:m": "prisma migrate dev", "p:m:r": "prisma migrate reset", "p:s": "prisma studio", "p:g": "prisma generate"}, "dependencies": {"@genkit-ai/googleai": "^1.10.0", "@prisma/client": "^6.8.2", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "docx": "^9.4.1", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "genkit": "^1.10.0", "helmet": "^8.0.0", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mammoth": "^1.9.0", "node-cache": "^5.1.2", "openai": "^4.89.0", "pdf-parse": "^1.1.1", "pdfkit": "^0.17.0", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/pdf-parse": "^1.1.5", "@types/pdfkit": "^0.13.9", "@types/uuid": "^10.0.0", "dotenv": "^16.4.7", "prisma": "^6.8.2", "tsx": "^4.19.2", "typescript": "^5"}}