# Database
DATABASE_URL="postgresql://username:password@localhost:5432/vocab_db"

# Server Configuration
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:5173

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=86400
JWT_COOKIE_NAME=vocab-auth-token

# AI/LLM Configuration
OPENAI_API_KEY=your-openai-api-key
GOOGLE_AI_API_KEY=your-google-ai-api-key

# Default Development User
DEFAULT_USER_PROVIDER=USERNAME_PASSWORD
DEFAULT_USER_PROVIDER_ID=dev-user-123
DEFAULT_USER_USERNAME=admin
DEFAULT_USER_PASSWORD=admin123
