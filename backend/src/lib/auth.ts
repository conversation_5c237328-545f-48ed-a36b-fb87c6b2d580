import { verifyToken } from '../backend/utils/token.util.js';
import { config } from '../config/index.js';

export interface AuthSession {
	user: {
		id: string;
		provider: string;
		provider_id: string;
	};
}

/**
 * Mock auth function for compatibility with existing API functions
 * In Express, authentication is handled by middleware
 * This function is kept for compatibility but should not be used
 */
export async function auth(): Promise<AuthSession | null> {
	throw new Error(
		'auth() function should not be used in Express backend. Use middleware authentication instead.'
	);
}

/**
 * Extract user from JWT token
 */
export async function getUserFromToken(token: string): Promise<AuthSession['user'] | null> {
	try {
		const decoded = await verifyToken(token);
		// Ensure the decoded token has the required properties
		if (
			decoded &&
			typeof decoded === 'object' &&
			'id' in decoded &&
			'provider' in decoded &&
			'provider_id' in decoded
		) {
			return decoded as AuthSession['user'];
		}
		return null;
	} catch (error) {
		console.error('Token verification failed:', error);
		return null;
	}
}

/**
 * Extract token from request headers or cookies
 */
export function extractTokenFromRequest(req: any): string | null {
	// Check Authorization header first (Bearer token)
	const authHeader = req.headers.authorization;
	if (authHeader && authHeader.startsWith('Bearer ')) {
		return authHeader.substring(7);
	}

	// Fallback to cookie
	const cookieToken = req.cookies?.[config.auth.jwtCookieName];
	if (cookieToken) {
		return cookieToken;
	}

	return null;
}
