import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import { config } from './config/index.js';
import { authRouter } from './routes/auth.js';
import { collectionsRouter } from './routes/collections.js';
import { feedbackRouter } from './routes/feedback.js';
import { keywordsRouter } from './routes/keywords.js';
import { llmRouter } from './routes/llm.js';
import { usersRouter } from './routes/users.js';
import { wordsRouter } from './routes/words.js';
import { errorHandler } from './middleware/error-handler.js';
import { requestLogger } from './middleware/request-logger.js';

const app = express();

// Security middleware
app.use(
	helmet({
		crossOriginEmbedderPolicy: false,
		contentSecurityPolicy: {
			directives: {
				defaultSrc: ["'self'"],
				styleSrc: ["'self'", "'unsafe-inline'"],
				scriptSrc: ["'self'"],
				imgSrc: ["'self'", 'data:', 'https:'],
			},
		},
	})
);

// CORS configuration
app.use(
	cors({
		origin: config.server.corsOrigin,
		credentials: true,
		methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
		allowedHeaders: ['Content-Type', 'Authorization', 'Cookie'],
	})
);

// Rate limiting
const limiter = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 100, // limit each IP to 100 requests per windowMs
	message: 'Too many requests from this IP, please try again later.',
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Request logging
app.use(requestLogger);

// Health check endpoint
app.get('/health', (req, res) => {
	res.json({
		status: 'ok',
		timestamp: new Date().toISOString(),
		environment: config.server.env,
	});
});

// API routes
app.use('/api/auth', authRouter);
app.use('/api/collections', collectionsRouter);
app.use('/api/feedback', feedbackRouter);
app.use('/api/keywords', keywordsRouter);
app.use('/api/llm', llmRouter);
app.use('/api/users', usersRouter);
app.use('/api/words', wordsRouter);

// 404 handler
app.use('*', (req, res) => {
	res.status(404).json({ error: 'Route not found' });
});

// Error handling middleware
app.use(errorHandler);

const port = config.server.port;

app.listen(port, () => {
	console.log(`🚀 Backend server running on port ${port}`);
	console.log(`📊 Environment: ${config.server.env}`);
	console.log(`🔗 CORS Origin: ${config.server.corsOrigin}`);
});

export default app;
