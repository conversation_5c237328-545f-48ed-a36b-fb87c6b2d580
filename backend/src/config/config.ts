import { Provider } from '@prisma/client';

const env = process.env;

// Unified configuration object
export const config = {
	server: {
		port: Number.parseInt(env.PORT || '3001', 10),
		env: env.NODE_ENV || 'development',
		corsOrigin: env.CORS_ORIGIN || 'http://localhost:5173',
	},
	auth: {
		jwtCookieName: env.JWT_COOKIE_NAME || 'vocab-auth-token',
		jwtSecret: env.JWT_SECRET || 'your-secret-key',
		jwtExpiresIn: Number(env.JWT_EXPIRES_IN) || 86400, // 24 hours
		// Default user for development environment
		defaultUser: {
			provider: Provider.USERNAME_PASSWORD,
			provider_id: env.DEFAULT_USER_PROVIDER_ID || 'dev-user-123',
			username: env.DEFAULT_USER_USERNAME || 'admin',
			password: env.DEFAULT_USER_PASSWORD || 'admin123',
		},
	},
	llm: {
		openAIKey: env.OPENAI_API_KEY || '',
		googleAIKey: env.GOOGLE_AI_API_KEY || '',
		openAIModel: env.LLM_OPENAI_MODEL || 'gpt-4o-mini',
		maxExamples: Number.parseInt(env.LLM_MAX_EXAMPLES || '8'),
		temperature: Number.parseFloat(env.LLM_TEMPERATURE || '0.7'),
		maxTokens: Number.parseInt(env.LLM_MAX_TOKENS || '1000'),
	},
	database: {
		url: env.DATABASE_URL || '',
	},
};

// Legacy async functions for backward compatibility
export async function getServerConfig() {
	return config.server;
}

export async function getAuthConfig() {
	return config.auth;
}

export async function getLLMConfig() {
	return config.llm;
}
