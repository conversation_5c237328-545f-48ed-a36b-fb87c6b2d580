import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '../backend/utils/token.util.js';
import { config } from '../config/index.js';

export interface AuthenticatedRequest extends Request {
	user?: {
		id: string;
		provider: string;
		provider_id: string;
	};
}

export async function authenticateToken(
	req: AuthenticatedRequest,
	res: Response,
	next: NextFunction
): Promise<void> {
	try {
		const authHeader = req.headers.authorization;
		const cookieToken = req.cookies?.[config.auth.jwtCookieName];

		let token: string | undefined;

		// Check Authorization header first (Bearer token)
		if (authHeader && authHeader.startsWith('Bearer ')) {
			token = authHeader.substring(7);
		}
		// Fallback to cookie
		else if (cookieToken) {
			token = cookieToken;
		}

		if (!token) {
			res.status(401).json({ error: 'Access token required' });
			return;
		}

		const decoded = await verifyToken(token);
		// Ensure the decoded token has the required properties
		if (
			decoded &&
			typeof decoded === 'object' &&
			'id' in decoded &&
			'provider' in decoded &&
			'provider_id' in decoded
		) {
			req.user = decoded as AuthenticatedRequest['user'];
		} else {
			throw new Error('Invalid token structure');
		}
		next();
	} catch (error) {
		console.error('Token verification failed:', error);
		res.status(401).json({ error: 'Invalid or expired token' });
	}
}

export function optionalAuth(req: AuthenticatedRequest, res: Response, next: NextFunction): void {
	// Try to authenticate but don't fail if no token
	authenticateToken(req, res, (error) => {
		// Continue regardless of authentication result
		next();
	});
}
