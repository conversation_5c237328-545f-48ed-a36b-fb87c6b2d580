import { Request, Response, NextFunction } from 'express';
import { ValidationError, UnauthorizedError, NotFoundError } from '../backend/errors/index.js';

export function errorHandler(
	error: Error,
	req: Request,
	res: Response,
	next: NextFunction
): void {
	console.error('Error:', error);

	if (error instanceof ValidationError) {
		res.status(400).json({ error: error.message });
		return;
	}

	if (error instanceof UnauthorizedError) {
		res.status(401).json({ error: error.message });
		return;
	}

	if (error instanceof NotFoundError) {
		res.status(404).json({ error: error.message });
		return;
	}

	// Default server error
	res.status(500).json({ 
		error: 'Internal server error',
		...(process.env.NODE_ENV === 'development' && { details: error.message })
	});
}
