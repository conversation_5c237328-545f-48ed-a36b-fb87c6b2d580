import { Router, Request, Response } from 'express';
import { submitFeedbackApi } from '../backend/api/feedback.api.js';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth.js';
import { ValidationError } from '../backend/errors/index.js';

const router = Router();

// Submit feedback (requires authentication)
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const { message } = req.body;

		const feedback = await submitFeedbackApi(req.user.id, message);

		res.status(201).json({
			success: true,
			message: 'Feedback submitted successfully',
			feedback,
		});
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Submit feedback error:', error);
		res.status(500).json({ error: 'Failed to submit feedback' });
	}
});

export { router as feedbackRouter };
