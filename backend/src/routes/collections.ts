import { Router, Request, Response } from 'express';
import {
	getColl<PERSON>tions<PERSON><PERSON>,
	getCollection<PERSON><PERSON>,
	createCollection<PERSON><PERSON>,
	deleteCollection<PERSON><PERSON>,
	addWordToCollectionApi,
	removeWordsFromCollectionApi,
} from '../backend/api/collection.api.js';
import {
	getCollectionStatsApi,
	trackWordReviewApi,
	trackQAPracticeSubmissionApi,
	trackParagraphPracticeSubmissionApi,
} from '../backend/api/collection-stats.api.js';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth.js';
import { ValidationError, NotFoundError } from '../backend/errors/index.js';

const router = Router();

// All collection routes require authentication
router.use(authenticateToken);

// Get all collections for user
router.get('/', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const collections = await getCollections<PERSON>pi(req.user.id);
		res.json(collections);
	} catch (error) {
		console.error('Get collections error:', error);
		res.status(500).json({ error: 'Failed to fetch collections' });
	}
});

// Get specific collection
router.get('/:id', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const { id } = req.params;
		const collection = await getCollectionApi(req.user.id, id);
		res.json(collection);
	} catch (error) {
		if (error instanceof NotFoundError) {
			res.status(404).json({ error: error.message });
			return;
		}
		console.error('Get collection error:', error);
		res.status(500).json({ error: 'Failed to fetch collection' });
	}
});

// Create new collection
router.post('/', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const { name, target_language, source_language, wordIds } = req.body;

		const collection = await createCollectionApi(
			req.user.id,
			name,
			target_language,
			source_language,
			wordIds
		);

		res.status(201).json(collection);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Create collection error:', error);
		res.status(500).json({ error: 'Failed to create collection' });
	}
});

// Delete collection
router.delete('/:id', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const { id } = req.params;
		const success = await deleteCollectionApi(req.user.id, id);

		if (success) {
			res.json({ success: true, message: 'Collection deleted successfully' });
		} else {
			res.status(500).json({ error: 'Failed to delete collection' });
		}
	} catch (error) {
		if (error instanceof NotFoundError) {
			res.status(404).json({ error: error.message });
			return;
		}
		console.error('Delete collection error:', error);
		res.status(500).json({ error: 'Failed to delete collection' });
	}
});

// Get collection statistics
router.get('/:id/stats', async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id: collectionId } = req.params;
		const days = req.query.days ? parseInt(req.query.days as string) : undefined;

		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const stats = await getCollectionStatsApi(collectionId, req.user.id, days);
		res.json(stats);
	} catch (error) {
		console.error('Get collection stats error:', error);
		res.status(500).json({ error: 'Failed to fetch collection stats' });
	}
});

// Track user activity
router.post('/:id/stats/track', async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id: collectionId } = req.params;
		const { type, count = 1 } = req.body;

		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		if (!type || !['word_review', 'qa_practice', 'paragraph_practice'].includes(type)) {
			res.status(400).json({
				error: 'Invalid tracking type. Must be one of: word_review, qa_practice, paragraph_practice',
			});
			return;
		}

		switch (type) {
			case 'word_review':
				await trackWordReviewApi(collectionId, req.user.id, count);
				break;
			case 'qa_practice':
				await trackQAPracticeSubmissionApi(collectionId, req.user.id, count);
				break;
			case 'paragraph_practice':
				await trackParagraphPracticeSubmissionApi(collectionId, req.user.id, count);
				break;
		}

		res.json({ success: true });
	} catch (error) {
		console.error('Track stats error:', error);
		res.status(500).json({ error: 'Failed to track stats' });
	}
});

export { router as collectionsRouter };
