import { Router, Request, Response } from 'express';
import {
	createUser<PERSON><PERSON>,
	getCurrent<PERSON>ser<PERSON>pi,
	getUserByIdApi,
	getUserByProviderApi,
} from '../backend/api/user.api.js';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth.js';
import { ValidationError, NotFoundError } from '../backend/errors/index.js';

const router = Router();

// Create user (public endpoint for registration)
router.post('/', async (req: Request, res: Response) => {
	try {
		const { provider, providerId, email, name } = req.body;

		const user = await createUserApi({ provider, providerId, email, name });

		res.status(201).json(user);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Create user error:', error);
		res.status(500).json({ error: 'Failed to create user' });
	}
});

// Get current user (requires authentication)
router.get('/me', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const user = await getCurrentUserApi(req.user.id);
		res.json(user);
	} catch (error) {
		console.error('Get current user error:', error);
		res.status(500).json({ error: 'Failed to get user information' });
	}
});

// Get user by ID (requires authentication)
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { id } = req.params;
		const user = await getUserByIdApi(id);

		if (!user) {
			res.status(404).json({ error: 'User not found' });
			return;
		}

		res.json(user);
	} catch (error) {
		if (error instanceof NotFoundError) {
			res.status(404).json({ error: error.message });
			return;
		}
		console.error('Get user by ID error:', error);
		res.status(500).json({ error: 'Failed to get user' });
	}
});

// Get user by provider (requires authentication)
router.get(
	'/provider/:provider/:providerId',
	authenticateToken,
	async (req: AuthenticatedRequest, res: Response) => {
		try {
			const { provider, providerId } = req.params;
			const user = await getUserByProviderApi(provider as any, providerId);

			if (!user) {
				res.status(404).json({ error: 'User not found' });
				return;
			}

			res.json(user);
		} catch (error) {
			if (error instanceof NotFoundError) {
				res.status(404).json({ error: error.message });
				return;
			}
			console.error('Get user by provider error:', error);
			res.status(500).json({ error: 'Failed to get user' });
		}
	}
);

// Update user endpoint not implemented yet

export { router as usersRouter };
