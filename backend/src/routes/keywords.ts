import { Router, Request, Response } from 'express';
import {
	getAllKeywordsOfUser<PERSON>pi,
	getKeyword<PERSON>pi,
	create<PERSON>eyword<PERSON><PERSON>,
	deleteKeywordApi,
} from '../backend/api/keyword.api.js';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth.js';
import { ValidationError, NotFoundError } from '../backend/errors/index.js';

const router = Router();

// All keyword routes require authentication
router.use(authenticateToken);

// Get all keywords for user
router.get('/', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const keywords = await getAllKeywordsOfUserApi(req.user.id);
		res.json(keywords);
	} catch (error) {
		console.error('Get keywords error:', error);
		res.status(500).json({ error: 'Failed to fetch keywords' });
	}
});

// Get specific keyword
router.get('/:id', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const { id } = req.params;
		const keyword = await getKeywordApi(req.user.id, id);
		res.json(keyword);
	} catch (error) {
		if (error instanceof NotFoundError) {
			res.status(404).json({ error: error.message });
			return;
		}
		console.error('Get keyword error:', error);
		res.status(500).json({ error: 'Failed to fetch keyword' });
	}
});

// Create new keyword
router.post('/', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const { name, description } = req.body;

		const keyword = await createKeywordApi(req.user.id, { name, description });

		res.status(201).json(keyword);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Create keyword error:', error);
		res.status(500).json({ error: 'Failed to create keyword' });
	}
});

// Delete keyword
router.delete('/:id', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const { id } = req.params;
		await deleteKeywordApi(req.user.id, id);
		res.json({ success: true, message: 'Keyword deleted successfully' });
	} catch (error) {
		if (error instanceof NotFoundError) {
			res.status(404).json({ error: error.message });
			return;
		}
		console.error('Delete keyword error:', error);
		res.status(500).json({ error: 'Failed to delete keyword' });
	}
});

export { router as keywordsRouter };
