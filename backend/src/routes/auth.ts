import { Router, Request, Response } from 'express';
import {
	usernamePassword<PERSON>ogin<PERSON>pi,
	logoutApi,
	providerLoginApi,
	developmentLoginApi,
} from '../backend/api/auth.api.js';
import { getCurrentUserApi } from '../backend/api/user.api.js';
import { ValidationError, UnauthorizedError } from '../backend/errors/index.js';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth.js';
import { config } from '../config/index.js';

const router = Router();

// Login with username/password
router.post('/login', async (req: Request, res: Response) => {
	try {
		const { username, password } = req.body;

		const token = await usernamePasswordLoginApi(username, password, res);

		res.json({
			success: true,
			message: 'Login successful',
			token,
		});
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}

		if (error instanceof UnauthorizedError) {
			res.status(401).json({ error: error.message });
			return;
		}

		console.error('Login error:', error);
		res.status(500).json({ error: 'Internal server error' });
	}
});

// Logout
router.post('/logout', async (req: Request, res: Response) => {
	try {
		await logoutApi();

		// Clear the auth cookie
		res.clearCookie(config.auth.jwtCookieName, {
			httpOnly: true,
			secure: config.server.env === 'production',
			sameSite: 'strict',
			path: '/',
		});

		res.json({
			success: true,
			message: 'Logout successful',
		});
	} catch (error) {
		console.error('Logout error:', error);
		res.status(500).json({ error: 'Internal server error' });
	}
});

// Get current user
router.get('/me', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const user = await getCurrentUserApi(req.user.id);
		res.json(user);
	} catch (error) {
		console.error('Get current user error:', error);
		res.status(500).json({ error: 'Failed to get user information' });
	}
});

// Development login (only in development)
router.post('/dev-login', async (req: Request, res: Response) => {
	try {
		if (config.server.env !== 'development') {
			res.status(403).json({ error: 'Development login only available in development mode' });
			return;
		}

		const token = await developmentLoginApi(res);

		res.json({
			success: true,
			message: 'Development login successful',
			token,
		});
	} catch (error) {
		console.error('Development login error:', error);
		res.status(500).json({ error: 'Internal server error' });
	}
});

export { router as authRouter };
