'use server';

import { ValidationError } from '@/backend/errors';
import { getFeedbackService } from '../wire.js';
import { Feedback } from '@prisma/client';

/**
 * Submits user feedback to the system.
 * @param userId - The authenticated user ID.
 * @param message - The feedback message content.
 * @returns The created feedback object.
 * @throws {ValidationError} If message is missing.
 * @throws {Error} If submission fails for other reasons.
 */
export async function submitFeedbackApi(userId: string, message: string): Promise<Feedback> {
	// Validate required fields
	if (!message) {
		throw new ValidationError('Message is required');
	}

	const feedbackService = getFeedbackService();
	try {
		const feedback = await feedbackService.createFeedback(message, userId);
		return feedback;
	} catch (error) {
		if (error instanceof ValidationError) {
			// Catch and rethrow if service throws ValidationError
			throw error;
		}
		// Log the detailed error for server-side debugging
		console.error('Failed to submit feedback:', error);
		// Throw a generic error for the client
		throw new Error('Failed to submit feedback');
	}
}
