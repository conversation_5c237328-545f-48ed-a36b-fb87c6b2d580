'use server';

import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLastSeenWordService } from '../wire.js';

/**
 * Saves the last seen word for the authenticated user.
 * @param wordId - The ID of the word that was last seen.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If wordId is missing.
 * @throws {Error} If saving the last seen word fails for other reasons (e.g., database error).
 */
export async function saveLastSeenWordApi(userId: string, wordId: string): Promise<void> {
	if (!wordId) {
		throw new ValidationError('Word ID is required to save the last seen word.');
	}

	const lastSeenWordService = getLastSeenWordService();

	try {
		// The SaveLastSeenWordCommand previously took (userId, wordId).
		// The SaveLastSeenWordCommandHandler called lastSeenWordService.saveLastSeenWord(command.userId, command.wordId).
		// Thus, the service method signature is expected to be saveLastSeenWord(userId: string, wordId: string).
		await lastSeenWordService.saveLastSeenWord(userId, wordId);
	} catch (error) {
		// Catch and rethrow specific known errors if applicable (e.g., if service throws specific custom errors)
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		// Log the detailed error for server-side debugging
		console.error(`Error in saveLastSeenWordApi for word ${wordId}, user ${userId}:`, error);
		// Throw a generic error for the client
		throw new Error('Failed to save the last seen word. Please try again.');
	}
}
