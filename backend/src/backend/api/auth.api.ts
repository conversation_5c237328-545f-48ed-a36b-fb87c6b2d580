import { UnauthorizedError, ValidationError } from '../errors/index.js';
import { generateToken } from '../utils/token.util.js';
import { getAuthService } from '../wire.js';
import { config } from '../../config/index.js';
import { Provider } from '@prisma/client';
import { Response } from 'express';

export async function logoutApi(res?: Response): Promise<void> {
	// Cookie clearing is handled in the route handler
	// This function is kept for compatibility
}

export async function providerLoginApi(
	provider: Provider,
	providerId: string,
	res?: Response
): Promise<string> {
	if (!provider || !providerId || !Object.values(Provider).includes(provider)) {
		throw new ValidationError('Invalid provider or provider ID');
	}

	const authService = getAuthService();
	const user = await authService.providerLogin(provider, providerId);
	if (!user) throw new UnauthorizedError('Authentication failed');

	const token = await generateToken(user);
	if (!token) throw new Error('Token generation failed');

	// Set cookie if response object is provided
	if (res) {
		res.cookie(config.auth.jwtCookieName, token, {
			httpOnly: true,
			secure: config.server.env === 'production',
			sameSite: 'strict',
			path: '/',
			maxAge: config.auth.jwtExpiresIn * 1000, // Convert to milliseconds
		});
	}

	return token;
}

export async function developmentLoginApi(res?: Response): Promise<string> {
	if (config.server.env !== 'development') {
		throw new Error('Development login is only available in development mode');
	}

	const defaultUser = config.auth.defaultUser;
	return await providerLoginApi(defaultUser.provider, defaultUser.provider_id, res);
}

/**
 * Đăng nhập bằng username và password
 * Tự động tạo tài khoản mới nếu username chưa tồn tại
 * @param username - Tên đăng nhập
 * @param password - Mật khẩu
 * @throws {ValidationError} Nếu thiếu username hoặc password
 * @throws {UnauthorizedError} Nếu mật khẩu không đúng
 */
export async function usernamePasswordLoginApi(
	username: string,
	password: string,
	res?: Response
): Promise<string> {
	if (!username || !password) {
		throw new ValidationError('Username and password are required');
	}

	if (username.trim().length < 3) {
		throw new ValidationError('Username must be at least 3 characters long');
	}

	if (password.length < 6) {
		throw new ValidationError('Password must be at least 6 characters long');
	}

	try {
		const authService = getAuthService();
		const user = await authService.usernamePasswordLogin(username.trim(), password);

		const token = await generateToken(user);
		if (!token) throw new Error('Token generation failed');

		// Set cookie if response object is provided
		if (res) {
			res.cookie(config.auth.jwtCookieName, token, {
				httpOnly: true,
				secure: config.server.env === 'production',
				sameSite: 'strict',
				path: '/',
				maxAge: config.auth.jwtExpiresIn * 1000, // Convert to milliseconds
			});
		}

		return token;
	} catch (error) {
		if (error instanceof Error && error.message === 'Invalid password') {
			throw new UnauthorizedError('Invalid username or password');
		}
		throw error;
	}
}
