'use server';

import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getKeywordService } from '../wire.js';
import { KeywordWithDetail } from '@/models';

/**
 * Deletes a keyword for the authenticated user.
 * @param keywordId - The ID of the keyword to delete.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If keywordId is missing.
 * @throws {NotFoundError} If keyword is not found by the service.
 * @throws {Error} For any other errors.
 */
export async function deleteKeywordApi(userId: string, keywordId: string): Promise<void> {
	if (!keywordId) {
		throw new ValidationError('Keyword ID is required for deletion.');
	}

	const keywordService = getKeywordService();
	try {
		const success = await keywordService.deleteKeyword(keywordId);
		if (typeof success === 'boolean' && !success) throw new NotFoundError('Keyword', keywordId);
	} catch (error) {
		if (error instanceof NotFoundError || error instanceof UnauthorizedError) throw error;
		console.error(`Error in deleteKeywordApi for keyword ${keywordId}, user ${userId}:`, error);
		throw new Error('Failed to delete keyword. Please try again.');
	}
}

/**
 * Updates a keyword for the authenticated user.
 * @param keywordId - The ID of the keyword to update.
 * @param data - Object containing `name` (string, required) and `description` (string, optional).
 * @returns The updated keyword.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If keywordId or name is missing.
 * @throws {NotFoundError} If keyword is not found.
 * @throws {Error} For any other errors.
 */
export async function updateKeywordApi(
	userId: string,
	keywordId: string,
	data: { name: string; description?: string }
): Promise<KeywordWithDetail> {
	if (!keywordId) {
		throw new ValidationError('Keyword ID is required for update.');
	}
	if (!data || typeof data !== 'object') {
		throw new ValidationError('Data object is required for update.');
	}
	const { name, description } = data;
	if (!name) {
		throw new ValidationError('Keyword name is required for update.');
	}

	const keywordService = getKeywordService();
	try {
		const updatedKeyword = await keywordService.updateKeyword(keywordId, name);
		if (!updatedKeyword) throw new NotFoundError('Keyword', keywordId);
		return updatedKeyword as KeywordWithDetail;
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError
		) {
			throw error;
		}
		console.error(`Error in updateKeywordApi for keyword ${keywordId}, user ${userId}:`, error);
		throw new Error('Failed to update keyword. Please try again.');
	}
}

/**
 * Retrieves a keyword by ID for the authenticated user.
 * @param keywordId - The ID of the keyword to retrieve.
 * @returns The keyword object.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If keywordId is missing.
 * @throws {NotFoundError} If keyword is not found or user is not authorized to view it.
 * @throws {Error} For any other errors.
 */
export async function getKeywordApi(userId: string, keywordId: string): Promise<KeywordWithDetail> {
	if (!keywordId) {
		throw new ValidationError('Keyword ID is required for retrieval.');
	}

	const keywordService = getKeywordService();
	try {
		const keyword = await keywordService.getKeywordById(keywordId);
		if (!keyword) throw new NotFoundError('Keyword', keywordId);
		return keyword as KeywordWithDetail;
	} catch (error) {
		if (
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError ||
			error instanceof ValidationError
		) {
			throw error;
		}
		console.error(`Error in getKeywordApi for keyword ${keywordId}, user ${userId}:`, error);
		throw new Error('Failed to retrieve keyword. Please try again.');
	}
}

/**
 * Creates a new keyword for the authenticated user.
 * @param data - Object containing `name` (string, required) and `description` (string, optional).
 * @returns The newly created keyword.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If name is missing or invalid.
 * @throws {Error} For any other errors (e.g., database errors, unexpected issues).
 */
export async function createKeywordApi(
	userId: string,
	data: {
		name: string;
		description?: string;
	}
): Promise<KeywordWithDetail> {
	if (!data || typeof data !== 'object') {
		throw new ValidationError('Data object is required for creation.');
	}
	const { name } = data;
	if (!name) {
		throw new ValidationError('Keyword name is required for creation.');
	}

	const keywordService = getKeywordService();
	try {
		const newKeyword = await keywordService.createKeyword(userId, name);
		return newKeyword as KeywordWithDetail;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			// e.g. duplicate name if service throws ValidationError
			throw error;
		}
		console.error(`Error in createKeywordApi for user ${userId} with name "${name}":`, error);
		throw new Error('Failed to create keyword. Please try again.');
	}
}

/**
 * Retrieves all keywords for the authenticated user.
 * @returns Array of keyword objects.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {Error} For any other errors.
 */
export async function getAllKeywordsOfUserApi(userId: string): Promise<KeywordWithDetail[]> {
	const keywordService = getKeywordService();
	try {
		// Assuming service.getUserKeywords(userId)
		const keywords = await keywordService.getUserKeywords(userId);
		return keywords as KeywordWithDetail[];
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in getAllKeywordsOfUserApi for user ${userId}:`, error);
		throw new Error('Failed to retrieve keywords. Please try again.');
	}
}
