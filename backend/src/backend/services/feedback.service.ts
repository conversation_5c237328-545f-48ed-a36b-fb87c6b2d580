import { FeedbackRepository } from '@/backend/repositories';
import { Feedback } from '@prisma/client';

export interface FeedbackService {
	createFeedback(message: string, userId: string): Promise<Feedback>;
}

export class FeedbackServiceImpl implements FeedbackService {
	constructor(private readonly getFeedbackRepository: () => FeedbackRepository) {}

	async createFeedback(message: string, userId: string): Promise<Feedback> {
		const createdFeedback = await this.getFeedbackRepository().create({
			message,
			user: {
				connect: { id: userId },
			},
		});

		const feedback = await this.getFeedbackRepository().findById(createdFeedback.id);
		if (!feedback) {
			throw new Error(
				`Failed to retrieve created feedback with ID ${createdFeedback.id} including user relation.`
			);
		}

		return feedback;
	}
}
