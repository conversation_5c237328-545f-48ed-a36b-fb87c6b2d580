import NodeCache from 'node-cache';

// Cache configuration
const DEFAULT_TTL = 60 * 60 * 24; // 24 hours in seconds

export class CacheService {
	private cache: NodeCache;

	constructor(ttlSeconds: number = DEFAULT_TTL) {
		this.cache = new NodeCache({
			stdTTL: ttlSeconds,
			checkperiod: ttlSeconds * 0.2,
			useClones: false,
		});
	}

	/**
	 * Get a value from cache
	 * @param key Cache key
	 * @returns Cached value or null if not found
	 */
	get<T>(key: string): T | null {
		const value = this.cache.get<T>(key);
		return value === undefined ? null : value;
	}

	/**
	 * Set a value in cache
	 * @param key Cache key
	 * @param value Value to cache
	 * @param ttl Time to live in seconds (optional)
	 * @returns true if successful
	 */
	set<T>(key: string, value: T, ttl: number = DEFAULT_TTL): boolean {
		return this.cache.set<T>(key, value, ttl);
	}

	/**
	 * Delete a value from cache
	 * @param key Cache key
	 * @returns true if successful
	 */
	del(key: string): boolean {
		return this.cache.del(key) > 0;
	}

	/**
	 * Clear all cache
	 */
	flush(): void {
		this.cache.flushAll();
	}

	/**
	 * Get cache stats
	 */
	getStats() {
		return this.cache.getStats();
	}

	/**
	 * Generate a cache key from parameters
	 * @param prefix Key prefix
	 * @param params Parameters to include in the key
	 * @returns Cache key
	 */
	generateKey(prefix: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				result[key] = params[key];
				return result;
			}, {});

		return `${prefix}:${JSON.stringify(sortedParams)}`;
	}
}
