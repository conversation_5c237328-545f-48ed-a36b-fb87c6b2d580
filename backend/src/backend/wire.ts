import {
	BaseRepository,
	BaseRepositoryImpl,
	CollectionRepository,
	CollectionRepositoryImpl,
	CollectionStatsRepository,
	CollectionStatsRepositoryImpl,
	KeywordRepository,
	KeywordRepositoryImpl,
	LastSeenWordRepository,
	LastSeenWordRepositoryImpl,
	UserRepository,
	UserRepositoryImpl,
	WordRepository,
	WordRepositoryImpl,
} from '@/backend/repositories';
import {
	AuthService,
	AuthServiceImpl,
	CacheService,
	CollectionService,
	CollectionServiceImpl,
	CollectionStatsService,
	CollectionStatsServiceImpl,
	FeedbackService,
	FeedbackServiceImpl,
	KeywordService,
	KeywordServiceImpl,
	LLMService,
	LastSeenWordService,
	LastSeenWordServiceImpl,
	UserService,
	UserServiceImpl,
	WordService,
	WordServiceImpl,
} from '@/backend/services';
import { Feedback, PrismaClient } from '@prisma/client';

let prismaClient: PrismaClient;
export const getPrismaClient = (): PrismaClient =>
	prismaClient || (prismaClient = new PrismaClient());

let collectionRepository: CollectionRepository;
export const getCollectionRepository = (): CollectionRepository =>
	collectionRepository ||
	(collectionRepository = new CollectionRepositoryImpl(getPrismaClient()));

let keywordRepository: KeywordRepository;
export const getKeywordRepository = (): KeywordRepository =>
	keywordRepository || (keywordRepository = new KeywordRepositoryImpl(getPrismaClient()));

let userRepository: UserRepository;
export const getUserRepository = (): UserRepository =>
	userRepository || (userRepository = new UserRepositoryImpl(getPrismaClient()));

let wordRepository: WordRepository;
export const getWordRepository = (): WordRepository =>
	wordRepository || (wordRepository = new WordRepositoryImpl(getPrismaClient()));

let lastSeenWordRepository: LastSeenWordRepository;
export const getLastSeenWordRepository = (): LastSeenWordRepository =>
	lastSeenWordRepository ||
	(lastSeenWordRepository = new LastSeenWordRepositoryImpl(getPrismaClient()));

let feedbackRepository: BaseRepository<Feedback>;
export const getFeedbackRepository = (): BaseRepository<Feedback> =>
	feedbackRepository ||
	(feedbackRepository = new BaseRepositoryImpl<Feedback>(getPrismaClient().feedback));

let collectionStatsRepository: CollectionStatsRepository;
export const getCollectionStatsRepository = (): CollectionStatsRepository =>
	collectionStatsRepository ||
	(collectionStatsRepository = new CollectionStatsRepositoryImpl(getPrismaClient()));

let userService: UserService | null = null;
export const getUserService = (): UserService =>
	userService || (userService = new UserServiceImpl(getUserRepository));

let authService: AuthService | null = null;
export const getAuthService = (): AuthService =>
	authService || (authService = new AuthServiceImpl(getUserService));

let cacheService: CacheService | null = null;
export const getCacheService = (): CacheService =>
	cacheService || (cacheService = new CacheService());

let feedbackService: FeedbackService | null = null;
export const getFeedbackService = (): FeedbackService =>
	feedbackService || (feedbackService = new FeedbackServiceImpl(getFeedbackRepository));

let lastSeenWordService: LastSeenWordService | null = null;
export const getLastSeenWordService = (): LastSeenWordService =>
	lastSeenWordService ||
	(lastSeenWordService = new LastSeenWordServiceImpl(getLastSeenWordRepository));

let llmService: LLMService | null = null;
export const getLLMService = (): LLMService =>
	llmService || (llmService = new LLMService(getWordService));

let wordService: WordService | null = null;
export const getWordService = (): WordService =>
	wordService ||
	(wordService = new WordServiceImpl(
		getWordRepository,
		getCollectionService,
		getLastSeenWordService
	));

let collectionService: CollectionService | null = null;
export const getCollectionService = (): CollectionService =>
	collectionService ||
	(collectionService = new CollectionServiceImpl(
		getCollectionRepository,
		getLLMService,
		getWordService
	));

let keywordService: any = null;
export const getKeywordService = (): KeywordService =>
	keywordService || (keywordService = new KeywordServiceImpl(getKeywordRepository));

let collectionStatsService: CollectionStatsService | null = null;
export const getCollectionStatsService = (): CollectionStatsService =>
	collectionStatsService ||
	(collectionStatsService = new CollectionStatsServiceImpl(getCollectionStatsRepository));
