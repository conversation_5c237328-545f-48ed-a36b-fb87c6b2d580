import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useCollectionsContext } from '../contexts/collections-context';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { ArrowLeftIcon, BookOpenIcon, BarChart3Icon } from 'lucide-react';
import { CollectionWithDetail } from '../models';

export function CollectionDetailPage() {
	const { id } = useParams<{ id: string }>();
	const { getCollection } = useCollectionsContext();
	const [collection, setCollection] = useState<CollectionWithDetail | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const loadCollection = async () => {
			if (!id) return;
			
			setIsLoading(true);
			try {
				const collectionData = await getCollection(id);
				setCollection(collectionData);
			} catch (error) {
				console.error('Failed to load collection:', error);
			} finally {
				setIsLoading(false);
			}
		};

		loadCollection();
	}, [id, getCollection]);

	if (isLoading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
					<p className="text-gray-600">Loading collection...</p>
				</div>
			</div>
		);
	}

	if (!collection) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<h2 className="text-xl font-semibold text-gray-900 mb-2">Collection not found</h2>
					<p className="text-gray-600 mb-4">The collection you're looking for doesn't exist.</p>
					<Link to="/collections">
						<Button>Back to Collections</Button>
					</Link>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50">
			<header className="bg-white shadow-sm border-b">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex items-center h-16">
						<Link to="/collections" className="mr-4">
							<Button variant="ghost" size="sm">
								<ArrowLeftIcon className="h-4 w-4 mr-2" />
								Back to Collections
							</Button>
						</Link>
						<h1 className="text-xl font-semibold text-gray-900">{collection.name}</h1>
					</div>
				</div>
			</header>

			<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div className="mb-8">
					<div className="flex justify-between items-start mb-4">
						<div>
							<h2 className="text-2xl font-bold text-gray-900 mb-2">{collection.name}</h2>
							<p className="text-gray-600">
								{collection.target_language} → {collection.source_language}
							</p>
						</div>
						<div className="flex space-x-2">
							<Button variant="outline">Edit Collection</Button>
							<Button>Start Practice</Button>
						</div>
					</div>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center">
								<BookOpenIcon className="h-5 w-5 mr-2" />
								Words
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold mb-2">
								{collection.word_ids?.length || 0}
							</div>
							<p className="text-sm text-gray-600">Total words in collection</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center">
								<BarChart3Icon className="h-5 w-5 mr-2" />
								Progress
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold mb-2">0%</div>
							<p className="text-sm text-gray-600">Words mastered</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Last Practice</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-sm text-gray-600 mb-2">Never</div>
							<Button variant="outline" className="w-full">
								Start Practice
							</Button>
						</CardContent>
					</Card>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					<Card>
						<CardHeader>
							<CardTitle>Words in Collection</CardTitle>
							<CardDescription>
								{collection.word_ids?.length || 0} words total
							</CardDescription>
						</CardHeader>
						<CardContent>
							{collection.word_ids && collection.word_ids.length > 0 ? (
								<div className="space-y-2">
									{collection.word_ids.slice(0, 10).map((wordId, index) => (
										<div key={wordId} className="flex items-center justify-between p-2 bg-gray-50 rounded">
											<span className="font-medium">Word {index + 1}</span>
											<Button variant="ghost" size="sm">
												View
											</Button>
										</div>
									))}
									{collection.word_ids.length > 10 && (
										<p className="text-sm text-gray-600 text-center pt-2">
											And {collection.word_ids.length - 10} more words...
										</p>
									)}
								</div>
							) : (
								<div className="text-center py-8">
									<p className="text-gray-600 mb-4">No words in this collection yet</p>
									<Button variant="outline">Add Words</Button>
								</div>
							)}
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Practice Options</CardTitle>
							<CardDescription>Choose how you want to practice</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								<Button className="w-full" disabled>
									Flashcards
								</Button>
								<Button variant="outline" className="w-full" disabled>
									Multiple Choice
								</Button>
								<Button variant="outline" className="w-full" disabled>
									Writing Practice
								</Button>
								<Button variant="outline" className="w-full" disabled>
									Listening Practice
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			</main>
		</div>
	);
}
