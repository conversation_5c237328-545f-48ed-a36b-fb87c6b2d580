import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuthContext } from '../contexts/auth-context';
import { useCollectionsContext } from '../contexts/collections-context';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { PlusIcon, BookOpenIcon, BarChart3Icon } from 'lucide-react';

export function HomePage() {
	const { user, logout } = useAuthContext();
	const { collections, getCollections } = useCollectionsContext();

	useEffect(() => {
		getCollections();
	}, [getCollections]);

	const handleLogout = async () => {
		await logout();
	};

	return (
		<div className="min-h-screen bg-gray-50">
			<header className="bg-white shadow-sm border-b">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center h-16">
						<h1 className="text-xl font-semibold text-gray-900">Vocab Learning</h1>
						<div className="flex items-center space-x-4">
							<span className="text-sm text-gray-600">Welcome, {user?.username || 'User'}</span>
							<Button variant="outline" onClick={handleLogout}>
								Logout
							</Button>
						</div>
					</div>
				</div>
			</header>

			<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div className="mb-8">
					<h2 className="text-2xl font-bold text-gray-900 mb-2">Dashboard</h2>
					<p className="text-gray-600">Manage your vocabulary collections and track your progress</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center">
								<BookOpenIcon className="h-5 w-5 mr-2" />
								Collections
							</CardTitle>
							<CardDescription>Manage your vocabulary collections</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold mb-2">{collections.length}</div>
							<Link to="/collections">
								<Button className="w-full">View Collections</Button>
							</Link>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center">
								<PlusIcon className="h-5 w-5 mr-2" />
								Quick Actions
							</CardTitle>
							<CardDescription>Start learning right away</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-2">
								<Link to="/collections">
									<Button variant="outline" className="w-full">
										Create Collection
									</Button>
								</Link>
								<Button variant="outline" className="w-full" disabled>
									Practice Words
								</Button>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center">
								<BarChart3Icon className="h-5 w-5 mr-2" />
								Progress
							</CardTitle>
							<CardDescription>Track your learning progress</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="text-sm text-gray-600 mb-2">Coming soon...</div>
							<Button variant="outline" className="w-full" disabled>
								View Stats
							</Button>
						</CardContent>
					</Card>
				</div>

				{collections.length > 0 && (
					<div>
						<h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Collections</h3>
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{collections.slice(0, 6).map((collection) => (
								<Card key={collection.id}>
									<CardHeader>
										<CardTitle className="text-base">{collection.name}</CardTitle>
										<CardDescription>
											{collection.word_ids?.length || 0} words
										</CardDescription>
									</CardHeader>
									<CardContent>
										<Link to={`/collections/${collection.id}`}>
											<Button variant="outline" className="w-full">
												Open Collection
											</Button>
										</Link>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				)}
			</main>
		</div>
	);
}
