import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../contexts/auth-context';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { toast } from 'sonner';

export function LoginPage() {
	const [username, setUsername] = useState('');
	const [password, setPassword] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const { login } = useAuthContext();
	const navigate = useNavigate();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsLoading(true);

		try {
			await login(username, password);
			toast.success('Login successful!');
			navigate('/');
		} catch (error) {
			toast.error(error instanceof Error ? error.message : 'Login failed');
		} finally {
			setIsLoading(false);
		}
	};

	const handleDevLogin = async () => {
		setIsLoading(true);
		try {
			// Call dev login API
			await fetch('/api/auth/dev-login', { method: 'POST' });
			toast.success('Development login successful!');
			navigate('/');
		} catch (error) {
			toast.error('Development login failed');
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50">
			<Card className="w-full max-w-md">
				<CardHeader>
					<CardTitle>Login to Vocab</CardTitle>
					<CardDescription>Enter your credentials to access your account</CardDescription>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSubmit} className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="username">Username</Label>
							<Input
								id="username"
								type="text"
								value={username}
								onChange={(e) => setUsername(e.target.value)}
								required
								disabled={isLoading}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="password">Password</Label>
							<Input
								id="password"
								type="password"
								value={password}
								onChange={(e) => setPassword(e.target.value)}
								required
								disabled={isLoading}
							/>
						</div>
						<Button type="submit" className="w-full" disabled={isLoading}>
							{isLoading ? 'Logging in...' : 'Login'}
						</Button>
					</form>
					
					{process.env.NODE_ENV === 'development' && (
						<div className="mt-4 pt-4 border-t">
							<Button 
								variant="outline" 
								className="w-full" 
								onClick={handleDevLogin}
								disabled={isLoading}
							>
								Development Login
							</Button>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
