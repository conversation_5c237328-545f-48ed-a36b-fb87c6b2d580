import { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useCollectionsContext } from '../contexts/collections-context';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { PlusIcon, BookOpenIcon, ArrowLeftIcon } from 'lucide-react';

export function CollectionsPage() {
	const { collections, getCollections, isLoading } = useCollectionsContext();

	useEffect(() => {
		getCollections();
	}, [getCollections]);

	if (isLoading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
					<p className="text-gray-600">Loading collections...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50">
			<header className="bg-white shadow-sm border-b">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex items-center h-16">
						<Link to="/" className="mr-4">
							<Button variant="ghost" size="sm">
								<ArrowLeftIcon className="h-4 w-4 mr-2" />
								Back
							</Button>
						</Link>
						<h1 className="text-xl font-semibold text-gray-900">Collections</h1>
					</div>
				</div>
			</header>

			<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div className="flex justify-between items-center mb-8">
					<div>
						<h2 className="text-2xl font-bold text-gray-900 mb-2">Your Collections</h2>
						<p className="text-gray-600">Manage and organize your vocabulary collections</p>
					</div>
					<Button>
						<PlusIcon className="h-4 w-4 mr-2" />
						Create Collection
					</Button>
				</div>

				{collections.length === 0 ? (
					<div className="text-center py-12">
						<BookOpenIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">No collections yet</h3>
						<p className="text-gray-600 mb-6">
							Create your first collection to start organizing your vocabulary
						</p>
						<Button>
							<PlusIcon className="h-4 w-4 mr-2" />
							Create Your First Collection
						</Button>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						{collections.map((collection) => (
							<Card key={collection.id} className="hover:shadow-md transition-shadow">
								<CardHeader>
									<CardTitle className="flex items-center justify-between">
										<span className="truncate">{collection.name}</span>
										<span className="text-sm font-normal text-gray-500">
											{collection.word_ids?.length || 0} words
										</span>
									</CardTitle>
									<CardDescription>
										{collection.target_language} → {collection.source_language}
									</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="flex space-x-2">
										<Link to={`/collections/${collection.id}`} className="flex-1">
											<Button variant="outline" className="w-full">
												Open
											</Button>
										</Link>
										<Button variant="outline" size="sm" disabled>
											Practice
										</Button>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				)}
			</main>
		</div>
	);
}
