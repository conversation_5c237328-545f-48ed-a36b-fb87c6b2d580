import { COLLECTIONS_LOADING_KEYS, LOADING_SCOPES } from '../constants';
import { collectionsApi } from '../lib/api-client';
import { CollectionWithDetail, WordDetail } from '../models';
import {
	createContext,
	Dispatch,
	ReactNode,
	SetStateAction,
	useCallback,
	useContext,
	useMemo,
	useState,
} from 'react';
import { useScopedLoading } from './loading-context';

type LoadingState = {
	fetch: boolean; // Loading collections list
	get: boolean; // Loading collection details
	getWordsToReview: boolean; // Loading words to review list
	search: boolean; // Searching collections
	create: boolean; // Creating new collection
	update: boolean; // Updating collection
	delete: boolean; // Deleting collection
	addTerm: boolean; // Adding term to collection
	addWords: boolean; // Adding words to collection
	removeWords: boolean; // Removing words from collection
	setCurrent: boolean; // Setting current collection

	// Words loading states
	wordsSearch: boolean; // Searching words
	fetchWords: boolean; // Fetching words by collection
	getWordsToReviewWords: boolean; // Getting words to review
	bulkDeleteWords: boolean; // Bulk deleting words
	fetchWord: boolean; // Fetching individual word
};

// Context type definition
export type CollectionsContextType = {
	// Collections state
	collections: CollectionWithDetail[];
	setCollections: Dispatch<SetStateAction<CollectionWithDetail[]>>;
	currentCollection: CollectionWithDetail | null;
	setCurrentCollection: Dispatch<SetStateAction<CollectionWithDetail | null>>;
	loading: LoadingState;
	error: Error | null;
	setError: Dispatch<SetStateAction<Error | null>>;
	currentCollectionWords: WordDetail[]; // Words specifically for current collection
	setCurrentCollectionWords: Dispatch<SetStateAction<WordDetail[]>>;

	// API methods
	getCollections: () => Promise<void>;
	getCollection: (id: string) => Promise<CollectionWithDetail>;
	createCollection: (data: {
		name: string;
		target_language: string;
		source_language: string;
		wordIds?: string[];
	}) => Promise<CollectionWithDetail>;
	deleteCollection: (id: string) => Promise<void>;
	isLoading: boolean;
};

const CollectionsContext = createContext<CollectionsContextType | undefined>(undefined);

export function CollectionsProvider({
	children,
	initialCollections,
}: {
	children: ReactNode;
	initialCollections?: CollectionWithDetail[];
}) {
	const [collections, setCollections] = useState<CollectionWithDetail[]>(
		initialCollections || []
	);
	const [currentCollection, setCurrentCollection] = useState<CollectionWithDetail | null>(null);
	const [error, setError] = useState<Error | null>(null);

	// Words state - unified for both general and current collection
	const [currentCollectionWords, setCurrentCollectionWords] = useState<WordDetail[]>([]);
	const { getLoading } = useScopedLoading(LOADING_SCOPES.COLLECTIONS);

	// API methods
	const getCollections = useCallback(async () => {
		try {
			const result = await collectionsApi.getAll();
			setCollections(result);
		} catch (err) {
			console.error('Failed to fetch collections:', err);
			setError(err as Error);
		}
	}, []);

	const getCollection = useCallback(async (id: string): Promise<CollectionWithDetail> => {
		try {
			const result = await collectionsApi.getById(id);
			return result;
		} catch (err) {
			console.error('Failed to fetch collection:', err);
			setError(err as Error);
			throw err;
		}
	}, []);

	const createCollection = useCallback(
		async (data: {
			name: string;
			target_language: string;
			source_language: string;
			wordIds?: string[];
		}): Promise<CollectionWithDetail> => {
			try {
				const result = await collectionsApi.create(data);
				setCollections((prev) => [...prev, result]);
				return result;
			} catch (err) {
				console.error('Failed to create collection:', err);
				setError(err as Error);
				throw err;
			}
		},
		[]
	);

	const deleteCollection = useCallback(async (id: string): Promise<void> => {
		try {
			await collectionsApi.delete(id);
			setCollections((prev) => prev.filter((c) => c.id !== id));
		} catch (err) {
			console.error('Failed to delete collection:', err);
			setError(err as Error);
			throw err;
		}
	}, []);

	// Create a loading state object for backward compatibility
	const loading: LoadingState = useMemo(
		() => ({
			fetch: getLoading(COLLECTIONS_LOADING_KEYS.FETCH),
			get: getLoading(COLLECTIONS_LOADING_KEYS.GET),
			getWordsToReview: getLoading(COLLECTIONS_LOADING_KEYS.GET_WORDS_TO_REVIEW),
			search: getLoading(COLLECTIONS_LOADING_KEYS.SEARCH),
			create: getLoading(COLLECTIONS_LOADING_KEYS.CREATE),
			update: getLoading(COLLECTIONS_LOADING_KEYS.UPDATE),
			delete: getLoading(COLLECTIONS_LOADING_KEYS.DELETE),
			addTerm: getLoading(COLLECTIONS_LOADING_KEYS.ADD_TERM),
			addWords: getLoading(COLLECTIONS_LOADING_KEYS.ADD_WORDS),
			removeWords: getLoading(COLLECTIONS_LOADING_KEYS.REMOVE_WORDS),
			setCurrent: getLoading(COLLECTIONS_LOADING_KEYS.SET_CURRENT),
			wordsSearch: getLoading(COLLECTIONS_LOADING_KEYS.WORDS_SEARCH),
			fetchWords: getLoading(COLLECTIONS_LOADING_KEYS.FETCH_WORDS),
			getWordsToReviewWords: getLoading(COLLECTIONS_LOADING_KEYS.GET_WORDS_TO_REVIEW_WORDS),
			bulkDeleteWords: getLoading(COLLECTIONS_LOADING_KEYS.BULK_DELETE_WORDS),
			fetchWord: getLoading(COLLECTIONS_LOADING_KEYS.FETCH_WORD),
		}),
		[getLoading]
	);

	const isLoading = useMemo(
		() => loading.fetch || loading.get || loading.create || loading.delete,
		[loading]
	);

	const contextValue = useMemo<CollectionsContextType>(
		() => ({
			collections,
			setCollections,
			currentCollection,
			setCurrentCollection,

			loading,
			error,
			setError,

			currentCollectionWords,
			setCurrentCollectionWords,

			// API methods
			getCollections,
			getCollection,
			createCollection,
			deleteCollection,
			isLoading,
		}),
		[
			collections,
			setCollections,
			currentCollection,
			setCurrentCollection,

			loading,
			error,
			setError,

			currentCollectionWords,
			setCurrentCollectionWords,

			getCollections,
			getCollection,
			createCollection,
			deleteCollection,
			isLoading,
		]
	);

	return (
		<CollectionsContext.Provider value={contextValue}>{children}</CollectionsContext.Provider>
	);
}

export { CollectionsContext };

export function useCollectionsContext() {
	const context = useContext(CollectionsContext);
	if (context === undefined) {
		throw new Error('useCollectionsContext must be used within a CollectionsProvider');
	}
	return context;
}
