import { authApi, usersApi } from '../lib/api-client';
import { AUTH_LOADING_KEYS, LOADING_SCOPES } from '../constants';
import { Provider } from '@prisma/client';
import { createContext, useCallback, useContext, useMemo, useState } from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';

type AuthContextType = {
	user: any | null;
	isLoading: boolean;
	error: Error | null;
	getUser: () => Promise<void>;
	login: (username: string, password: string) => Promise<void>;
	providerLogin: (provider: Provider, provider_id: string) => Promise<void>;
	logout: () => Promise<void>;
	getUserByProvider: (provider: Provider, providerId: string) => Promise<any | null>;
	clearError: () => void;
	getLoadingState: (key: string) => boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
	const [user, setUser] = useState<any | null>(null);
	const [error, setError] = useState<Error | null>(null);

	const { getLoading } = useScopedLoading(LOADING_SCOPES.AUTH);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.AUTH);

	const isLoading = useMemo(
		() =>
			getLoading(AUTH_LOADING_KEYS.GET_USER) ||
			getLoading(AUTH_LOADING_KEYS.PROVIDER_LOGIN) ||
			getLoading(AUTH_LOADING_KEYS.LOGOUT) ||
			getLoading(AUTH_LOADING_KEYS.GET_USER_BY_PROVIDER),
		[getLoading]
	);

	const handleApiError = useCallback((err: unknown, defaultMessage: string) => {
		return err instanceof Error ? err : new Error(defaultMessage);
	}, []);

	const getUser = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, AUTH_LOADING_KEYS.GET_USER);
		start();

		try {
			const result = await authApi.getCurrentUser();
			setUser(result);
			end();
		} catch (err) {
			end(handleApiError(err, 'Failed to get current user'));
		}
	}, [loadingErrorHelper, handleApiError]);

	const login = useCallback(
		async (username: string, password: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				AUTH_LOADING_KEYS.PROVIDER_LOGIN
			);
			start();

			try {
				await authApi.login(username, password);
				await getUser();
				end();
			} catch (err) {
				end(handleApiError(err, 'Failed to login'));
			}
		},
		[getUser, loadingErrorHelper, handleApiError]
	);

	const providerLogin = useCallback(
		async (provider: Provider, provider_id: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				AUTH_LOADING_KEYS.PROVIDER_LOGIN
			);
			start();

			try {
				await providerLoginApi(provider, provider_id);
				await getUser();
				end();
			} catch (err) {
				end(handleApiError(err, 'Failed to login with provider'));
			}
		},
		[getUser, loadingErrorHelper, handleApiError]
	);

	const logout = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, AUTH_LOADING_KEYS.LOGOUT);
		start();

		try {
			await authApi.logout();
			setUser(null);
			end();
		} catch (err) {
			end(handleApiError(err, 'Failed to logout'));
		}
	}, [loadingErrorHelper, handleApiError]);

	const getUserByProvider = useCallback(
		async (provider: Provider, providerId: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				AUTH_LOADING_KEYS.GET_USER_BY_PROVIDER
			);
			start();

			try {
				const result = await usersApi.getByProvider(provider, providerId);
				end();
				return result;
			} catch (err) {
				end(handleApiError(err, 'Failed to get user by provider'));
				return null;
			}
		},
		[loadingErrorHelper, handleApiError]
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	const value = useMemo(
		() => ({
			user,
			isLoading,
			error,
			getUser,
			login,
			providerLogin,
			logout,
			getUserByProvider,
			clearError,
			getLoadingState: getLoading,
		}),
		[
			user,
			isLoading,
			error,
			getUser,
			login,
			providerLogin,
			logout,
			getUserByProvider,
			clearError,
			getLoading,
		]
	);

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuthContext() {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuthContext must be used within an AuthProvider');
	}
	return context;
}

export function useAuth() {
	return useAuthContext();
}
