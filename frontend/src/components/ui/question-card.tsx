import { <PERSON><PERSON>, <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui';
import { cn } from '@/lib';
import { Translate } from './translate';

interface QuestionCardProps {
	question: {
		id: string;
		question: string;
		options: string[];
		word: string;
	};
	selectedAnswer?: string;
	onAnswerSelect: (questionId: string, answer: string) => void;
	questionNumber: number;
}

export function QuestionCard({
	question,
	selectedAnswer,
	onAnswerSelect,
	questionNumber,
}: QuestionCardProps) {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="text-lg">
					<Translate text="Question" /> {questionNumber}
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					<p className="text-lg font-medium">
						<Translate text={question.question} />
					</p>
					<div className="space-y-2">
						{question.options.map((option, index) => (
							<Button
								key={index}
								variant="outline"
								className={cn(
									'w-full justify-start text-left',
									selectedAnswer === option &&
										'bg-primary text-primary-foreground'
								)}
								onClick={() => onAnswerSelect(question.id, option)}
							>
								{option}
							</Button>
						))}
					</div>
					<p className="text-sm text-muted-foreground">
						<Translate text="Word" />: {question.word}
					</p>
				</div>
			</CardContent>
		</Card>
	);
}
