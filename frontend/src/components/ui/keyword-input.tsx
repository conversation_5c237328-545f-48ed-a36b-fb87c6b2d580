import { Button, Input, Label } from '@/components/ui';

interface KeywordInputProps {
	keywords: string[];
	currentKeyword: string;
	label?: string;
	placeholder?: string;
	onKeywordsChange: (keywords: string[]) => void;
	onCurrentKeywordChange: (keyword: string) => void;
}

export function KeywordInput({
	keywords,
	currentKeyword,
	label = 'Keywords',
	placeholder = 'Enter keyword and press Enter or Add',
	onKeywordsChange,
	onCurrentKeywordChange,
}: KeywordInputProps) {
	const handleAddKeyword = () => {
		if (currentKeyword.trim() && !keywords.includes(currentKeyword.trim())) {
			onKeywordsChange([...keywords, currentKeyword.trim()]);
			onCurrentKeywordChange('');
		}
	};

	const handleRemoveKeyword = (keywordToRemove: string) => {
		onKeywordsChange(keywords.filter((keyword) => keyword !== keywordToRemove));
	};

	return (
		<div>
			<Label>{label}</Label>
			<div className="flex gap-2 mt-1">
				<Input
					value={currentKeyword}
					onChange={(e) => onCurrentKeywordChange(e.target.value)}
					placeholder={placeholder}
					onKeyDown={(e) => e.key === 'Enter' && handleAddKeyword()}
				/>
				<Button onClick={handleAddKeyword} variant="secondary">
					Add
				</Button>
			</div>
			<div className="flex flex-wrap gap-2 mt-2">
				{keywords.map((keyword) => (
					<div
						key={keyword}
						className="bg-primary/10 px-3 py-1 rounded-full flex items-center gap-2"
					>
						<span>{keyword}</span>
						<button
							onClick={() => handleRemoveKeyword(keyword)}
							className="text-primary hover:text-primary/80"
						>
							×
						</button>
					</div>
				))}
			</div>
		</div>
	);
}
