'use client';

import { Translate } from '@/components/ui';

interface ErrorDisplayProps {
	error: Error | null;
	onDismiss: () => void;
}

export function ErrorDisplay({ error, onDismiss }: ErrorDisplayProps) {
	if (!error) return null;

	return (
		<div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
			<p className="text-sm text-red-600 dark:text-red-400">{error.message}</p>
			<button
				onClick={onDismiss}
				className="mt-2 text-xs text-red-600 dark:text-red-400 underline hover:no-underline"
			>
				<Translate text="ui.dismiss" />
			</button>
		</div>
	);
}
