'use client';

import { cn } from '@/lib';
import React from 'react';

interface FocusTrapProps {
	children: React.ReactNode;
	className?: string;
	active?: boolean;
}

export const FocusTrap = React.forwardRef<HTMLDivElement, FocusTrapProps>(
	({ children, className, active = true }, ref) => {
		const containerRef = React.useRef<HTMLDivElement>(null);

		React.useEffect(() => {
			if (!active || !containerRef.current) return;

			const container = containerRef.current;
			const focusableElements = container.querySelectorAll(
				'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
			);
			const firstFocusable = focusableElements[0] as HTMLElement;
			const lastFocusable = focusableElements[focusableElements.length - 1] as HTMLElement;

			const handleTabKey = (e: KeyboardEvent) => {
				if (e.key !== 'Tab') return;

				if (e.shiftKey) {
					if (document.activeElement === firstFocusable) {
						lastFocusable.focus();
						e.preventDefault();
					}
				} else {
					if (document.activeElement === lastFocusable) {
						firstFocusable.focus();
						e.preventDefault();
					}
				}
			};

			container.addEventListener('keydown', handleTabKey);
			return () => container.removeEventListener('keydown', handleTabKey);
		}, [active]);

		return (
			<div ref={containerRef} className={cn('focus-trap', className)}>
				{children}
			</div>
		);
	}
);

FocusTrap.displayName = 'FocusTrap';
