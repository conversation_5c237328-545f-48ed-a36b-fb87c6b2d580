'use client';

import { Input } from '@/components/ui';
import { cn } from '@/lib';
import { Search } from 'lucide-react';

interface SearchInputProps {
	placeholder?: string;
	value: string;
	onChange: (value: string) => void;
	onSearch?: () => void;
	className?: string;
}

export function SearchInput({
	placeholder = 'Search...',
	value,
	onChange,
	onSearch,
	className,
}: SearchInputProps) {
	return (
		<div className={cn('relative', className)}>
			<Search className="absolute left-2 top-3 h-4 w-4 text-muted-foreground z-10" />
			<Input
				placeholder={placeholder}
				value={value}
				onChange={(e) => onChange(e.target.value)}
				onKeyDown={(e) => {
					if (e.key === 'Enter') {
						onSearch?.();
					}
				}}
				className="pl-8"
			/>
		</div>
	);
}
