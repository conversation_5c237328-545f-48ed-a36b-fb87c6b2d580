'use client';

import React from 'react';

import { cn } from '@/lib';

const Card = React.forwardRef<
	HTMLDivElement,
	React.HTMLAttributes<HTMLDivElement> & {
		role?: string;
		'aria-label'?: string;
		'aria-labelledby'?: string;
		'aria-describedby'?: string;
	}
>(({ className, role = 'region', ...props }, ref) => (
	<div
		ref={ref}
		className={cn('rounded-lg border bg-card text-card-foreground shadow-sm', className)}
		role={role}
		{...props}
	/>
));
Card.displayName = 'Card';

const CardHeader = React.forwardRef<
	HTMLDivElement,
	React.HTMLAttributes<HTMLDivElement> & {
		role?: string;
		'aria-label'?: string;
	}
>(({ className, role = 'group', ...props }, ref) => (
	<div
		ref={ref}
		className={cn('flex flex-col space-y-1.5 p-6', className)}
		role={role}
		{...props}
	/>
));
CardHeader.displayName = 'CardHeader';

const CardTitle = React.forwardRef<
	HTMLParagraphElement,
	React.HTMLAttributes<HTMLHeadingElement> & {
		as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
		'aria-label'?: string;
	}
>(({ className, as: Component = 'h3', ...props }, ref) => (
	<Component
		ref={ref}
		className={cn('font-semibold leading-none tracking-tight', className)}
		{...props}
	/>
));
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef<
	HTMLParagraphElement,
	React.HTMLAttributes<HTMLParagraphElement> & {
		'aria-label'?: string;
	}
>(({ className, ...props }, ref) => (
	<p ref={ref} className={cn('text-sm text-muted-foreground', className)} {...props} />
));
CardDescription.displayName = 'CardDescription';

const CardContent = React.forwardRef<
	HTMLDivElement,
	React.HTMLAttributes<HTMLDivElement> & {
		role?: string;
		'aria-label'?: string;
	}
>(({ className, role = 'group', ...props }, ref) => (
	<div ref={ref} className={cn('p-6 pt-0', className)} role={role} {...props} />
));
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef<
	HTMLDivElement,
	React.HTMLAttributes<HTMLDivElement> & {
		role?: string;
		'aria-label'?: string;
	}
>(({ className, role = 'group', ...props }, ref) => (
	<div ref={ref} className={cn('flex items-center p-6 pt-0', className)} role={role} {...props} />
));
CardFooter.displayName = 'CardFooter';

export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle };

