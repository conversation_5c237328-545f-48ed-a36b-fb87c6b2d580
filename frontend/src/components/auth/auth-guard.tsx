import { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthContext } from '../../contexts/auth-context';

interface AuthGuardProps {
	children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
	const { user, getUser, isLoading } = useAuthContext();

	useEffect(() => {
		// Try to get user on mount if not already loaded
		if (!user && !isLoading) {
			getUser().catch(() => {
				// Ignore errors here, will redirect to login
			});
		}
	}, [user, isLoading, getUser]);

	// Show loading while checking authentication
	if (isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
					<p className="text-gray-600">Loading...</p>
				</div>
			</div>
		);
	}

	// Redirect to login if not authenticated
	if (!user) {
		return <Navigate to="/login" replace />;
	}

	// Render children if authenticated
	return <>{children}</>;
}
