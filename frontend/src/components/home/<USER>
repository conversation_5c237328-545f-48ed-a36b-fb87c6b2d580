'use client';

import { Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { BookO<PERSON>, Brain, Sparkles } from 'lucide-react';
import { itemVariants } from './animation-variants';

export function HeroSection() {
	return (
		<motion.div variants={itemVariants} className="space-y-6 relative">
			{/* Floating icons animation */}
			<div className="absolute inset-0 overflow-hidden pointer-events-none">
				<motion.div
					animate={{
						y: [0, -10, 0],
						rotate: [0, 5, 0],
					}}
					transition={{
						duration: 4,
						repeat: Infinity,
						ease: 'easeInOut',
					}}
					className="absolute top-10 left-1/4 text-primary/20"
				>
					<BookOpen className="size-8" />
				</motion.div>
				<motion.div
					animate={{
						y: [0, 10, 0],
						rotate: [0, -5, 0],
					}}
					transition={{
						duration: 3,
						repeat: Infinity,
						ease: 'easeInOut',
						delay: 1,
					}}
					className="absolute top-20 right-1/4 text-secondary/20"
				>
					<Brain className="size-10" />
				</motion.div>
				<motion.div
					animate={{
						y: [0, -8, 0],
						scale: [1, 1.1, 1],
					}}
					transition={{
						duration: 2.5,
						repeat: Infinity,
						ease: 'easeInOut',
						delay: 0.5,
					}}
					className="absolute top-5 right-1/3 text-primary/30"
				>
					<Sparkles className="size-6" />
				</motion.div>
			</div>

			<motion.h1
				className="text-5xl md:text-7xl font-extrabold bg-gradient-to-r from-primary via-secondary to-primary bg-clip-text text-transparent"
				initial={{ opacity: 0, y: -20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
			>
				<Translate text="home.title" />
			</motion.h1>

			<motion.p
				className="text-muted-foreground text-xl max-w-3xl mx-auto leading-relaxed"
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.2 }}
			>
				<Translate text="home.enhanced_subtitle" />
			</motion.p>

			{/* Feature badges */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.4 }}
				className="flex flex-wrap justify-center gap-3 mt-6"
			>
				<span className="px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium border border-primary/20">
					<Brain className="size-4 inline mr-1" />
					<Translate text="home.ai_powered" />
				</span>
				<span className="px-4 py-2 bg-secondary/10 text-secondary rounded-full text-sm font-medium border border-secondary/20">
					<BookOpen className="size-4 inline mr-1" />
					<Translate text="home.multilingual" />
				</span>
				<span className="px-4 py-2 bg-green-500/10 text-green-600 dark:text-green-400 rounded-full text-sm font-medium border border-green-500/20">
					<Sparkles className="size-4 inline mr-1" />
					<Translate text="home.smart_practice" />
				</span>
			</motion.div>
		</motion.div>
	);
}
