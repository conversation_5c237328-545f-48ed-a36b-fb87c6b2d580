/**
 * Text diffing utilities for highlighting differences between original and corrected text
 */
export interface DiffResult {
	type: 'equal' | 'delete' | 'insert';
	text: string;
}

/**
 * Simple word-based diff algorithm
 * Compares two texts word by word and identifies differences
 */
export function diffTexts(original: string, corrected: string): DiffResult[] {
	const originalWords = original.split(/\s+/);
	const correctedWords = corrected.split(/\s+/);

	const result: DiffResult[] = [];
	let i = 0,
		j = 0;

	while (i < originalWords.length || j < correctedWords.length) {
		if (i >= originalWords.length) {
			// Remaining words in corrected are insertions
			result.push({ type: 'insert', text: correctedWords[j] });
			j++;
		} else if (j >= correctedWords.length) {
			// Remaining words in original are deletions
			result.push({ type: 'delete', text: originalWords[i] });
			i++;
		} else if (originalWords[i] === correctedWords[j]) {
			// Words are the same
			result.push({ type: 'equal', text: originalWords[i] });
			i++;
			j++;
		} else {
			// Words are different - look ahead to find best match
			let foundMatch = false;

			// Look for the corrected word in upcoming original words
			for (let k = i + 1; k < Math.min(i + 5, originalWords.length); k++) {
				if (originalWords[k] === correctedWords[j]) {
					// Found a match - mark intermediate words as deletions
					for (let l = i; l < k; l++) {
						result.push({ type: 'delete', text: originalWords[l] });
					}
					result.push({ type: 'equal', text: originalWords[k] });
					i = k + 1;
					j++;
					foundMatch = true;
					break;
				}
			}

			if (!foundMatch) {
				// Look for the original word in upcoming corrected words
				for (let k = j + 1; k < Math.min(j + 5, correctedWords.length); k++) {
					if (correctedWords[k] === originalWords[i]) {
						// Found a match - mark intermediate words as insertions
						for (let l = j; l < k; l++) {
							result.push({ type: 'insert', text: correctedWords[l] });
						}
						result.push({ type: 'equal', text: correctedWords[k] });
						j = k + 1;
						i++;
						foundMatch = true;
						break;
					}
				}
			}

			if (!foundMatch) {
				// No match found - treat as substitution
				result.push({ type: 'delete', text: originalWords[i] });
				result.push({ type: 'insert', text: correctedWords[j] });
				i++;
				j++;
			}
		}
	}

	return result;
}

export interface HighlightedDiff {
	type: 'equal' | 'delete' | 'insert' | 'change';
	text: string;
	wrong?: string;
	correct?: string;
	className?: string;
}

/**
 * Generate structured diff data for text differences
 */
export function highlightTextDifferences(original: string, corrected: string): HighlightedDiff[] {
	if (!original || !corrected) {
		return [];
	}

	const diffs = diffTexts(original, corrected);
	const result: HighlightedDiff[] = [];

	for (let i = 0; i < diffs.length; i++) {
		const diff = diffs[i];

		if (diff.type === 'equal') {
			result.push({
				type: 'equal',
				text: diff.text,
			});
		} else if (diff.type === 'delete') {
			// Check if next item is insert - if so, combine them
			if (i + 1 < diffs.length && diffs[i + 1].type === 'insert') {
				result.push({
					type: 'change',
					text: diffs[i + 1].text, // Use corrected text as main text
					wrong: diff.text,
					correct: diffs[i + 1].text,
				});
				i++; // Skip the next insert item
			} else {
				// Standalone delete
				result.push({
					type: 'delete',
					text: diff.text,
				});
			}
		} else if (diff.type === 'insert') {
			// Standalone insert (not preceded by delete)
			result.push({
				type: 'insert',
				text: diff.text,
			});
		}
	}

	return result;
}
