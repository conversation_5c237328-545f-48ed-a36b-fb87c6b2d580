import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
	baseURL: `${API_BASE_URL}/api`,
	timeout: 30000,
	withCredentials: true, // Include cookies for authentication
	headers: {
		'Content-Type': 'application/json',
	},
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
	(config) => {
		// Get token from localStorage if available
		const token = localStorage.getItem('auth-token');
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}
		return config;
	},
	(error) => {
		return Promise.reject(error);
	}
);

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
	(response: AxiosResponse) => {
		return response;
	},
	(error) => {
		if (error.response?.status === 401) {
			// Clear token and redirect to login
			localStorage.removeItem('auth-token');
			window.location.href = '/login';
		}
		return Promise.reject(error);
	}
);

// Auth API
export const authApi = {
	login: async (username: string, password: string) => {
		const response = await apiClient.post('/auth/login', { username, password });
		if (response.data.token) {
			localStorage.setItem('auth-token', response.data.token);
		}
		return response.data;
	},

	logout: async () => {
		await apiClient.post('/auth/logout');
		localStorage.removeItem('auth-token');
	},

	getCurrentUser: async () => {
		const response = await apiClient.get('/auth/me');
		return response.data;
	},

	devLogin: async () => {
		const response = await apiClient.post('/auth/dev-login');
		if (response.data.token) {
			localStorage.setItem('auth-token', response.data.token);
		}
		return response.data;
	},
};

// Collections API
export const collectionsApi = {
	getAll: async () => {
		const response = await apiClient.get('/collections');
		return response.data;
	},

	getById: async (id: string) => {
		const response = await apiClient.get(`/collections/${id}`);
		return response.data;
	},

	create: async (data: {
		name: string;
		target_language: string;
		source_language: string;
		wordIds?: string[];
	}) => {
		const response = await apiClient.post('/collections', data);
		return response.data;
	},

	delete: async (id: string) => {
		const response = await apiClient.delete(`/collections/${id}`);
		return response.data;
	},

	getStats: async (id: string, days?: number) => {
		const params = days ? { days } : {};
		const response = await apiClient.get(`/collections/${id}/stats`, { params });
		return response.data;
	},

	trackActivity: async (id: string, type: string, count = 1) => {
		const response = await apiClient.post(`/collections/${id}/stats/track`, { type, count });
		return response.data;
	},
};

// Words API
export const wordsApi = {
	search: async (q: string, language?: string, limit?: number) => {
		const params = { q, language, limit };
		const response = await apiClient.get('/words/search', { params });
		return response.data;
	},

	getById: async (id: string) => {
		const response = await apiClient.get(`/words/${id}`);
		return response.data;
	},

	getDetails: async (id: string, sourceLanguage?: string, targetLanguage?: string) => {
		const params = { sourceLanguage, targetLanguage };
		const response = await apiClient.get(`/words/${id}/details`, { params });
		return response.data;
	},

	saveLastSeen: async (id: string) => {
		const response = await apiClient.post(`/words/${id}/last-seen`);
		return response.data;
	},
};

// Keywords API
export const keywordsApi = {
	getAll: async () => {
		const response = await apiClient.get('/keywords');
		return response.data;
	},

	getById: async (id: string) => {
		const response = await apiClient.get(`/keywords/${id}`);
		return response.data;
	},

	create: async (data: { name: string; description?: string }) => {
		const response = await apiClient.post('/keywords', data);
		return response.data;
	},

	delete: async (id: string) => {
		const response = await apiClient.delete(`/keywords/${id}`);
		return response.data;
	},
};

// LLM API
export const llmApi = {
	generateRandomWords: async (data: any) => {
		const response = await apiClient.post('/llm/generate-random-words', data);
		return response.data;
	},

	generateWordDetails: async (data: any) => {
		const response = await apiClient.post('/llm/generate-word-details', data);
		return response.data;
	},

	generateParagraph: async (data: any) => {
		const response = await apiClient.post('/llm/generate-paragraph', data);
		return response.data;
	},

	generateQuestions: async (data: any) => {
		const response = await apiClient.post('/llm/generate-questions', data);
		return response.data;
	},

	generateParagraphWithQuestions: async (data: any) => {
		const response = await apiClient.post('/llm/generate-paragraph-with-questions', data);
		return response.data;
	},

	evaluateAnswers: async (data: any) => {
		const response = await apiClient.post('/llm/evaluate-answers', data);
		return response.data;
	},

	evaluateTranslation: async (data: any) => {
		const response = await apiClient.post('/llm/evaluate-translation', data);
		return response.data;
	},

	grammarPractice: async (data: any) => {
		const response = await apiClient.post('/llm/grammar-practice', data);
		return response.data;
	},
};

// Feedback API
export const feedbackApi = {
	submit: async (message: string) => {
		const response = await apiClient.post('/feedback', { message });
		return response.data;
	},
};

// Users API
export const usersApi = {
	create: async (data: {
		provider: string;
		providerId: string;
		email?: string;
		name?: string;
	}) => {
		const response = await apiClient.post('/users', data);
		return response.data;
	},

	getById: async (id: string) => {
		const response = await apiClient.get(`/users/${id}`);
		return response.data;
	},

	getByProvider: async (provider: string, providerId: string) => {
		const response = await apiClient.get(`/users/provider/${provider}/${providerId}`);
		return response.data;
	},

	update: async (id: string, data: any) => {
		const response = await apiClient.put(`/users/${id}`, data);
		return response.data;
	},
};

export default apiClient;
