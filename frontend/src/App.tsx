import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/auth-context';
import { LoadingProvider } from './contexts/loading-context';
import { TranslationProvider } from './contexts/translation-context';
import { CollectionsProvider } from './contexts/collections-context';
import { LLMProvider } from './contexts/llm-context';
import { MediaQueryProvider } from './contexts/media-query-context';
import { AuthGuard } from './components/auth/auth-guard';
import { LoginPage } from './pages/login';
import { HomePage } from './pages/home';
import { CollectionsPage } from './pages/collections';
import { CollectionDetailPage } from './pages/collection-detail';
import { Toaster } from 'sonner';

function App() {
	return (
		<Router>
			<MediaQueryProvider>
				<TranslationProvider>
					<LoadingProvider>
						<AuthProvider>
							<LLMProvider>
								<CollectionsProvider>
									<div className="min-h-screen bg-background">
										<Routes>
											<Route path="/login" element={<LoginPage />} />
											<Route
												path="/"
												element={
													<AuthGuard>
														<HomePage />
													</AuthGuard>
												}
											/>
											<Route
												path="/collections"
												element={
													<AuthGuard>
														<CollectionsPage />
													</AuthGuard>
												}
											/>
											<Route
												path="/collections/:id"
												element={
													<AuthGuard>
														<CollectionDetailPage />
													</AuthGuard>
												}
											/>
											<Route path="*" element={<Navigate to="/" replace />} />
										</Routes>
										<Toaster />
									</div>
								</CollectionsProvider>
							</LLMProvider>
						</AuthProvider>
					</LoadingProvider>
				</TranslationProvider>
			</MediaQueryProvider>
		</Router>
	);
}

export default App;
