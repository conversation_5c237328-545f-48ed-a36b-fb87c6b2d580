# Vocab App Migration: Next.js to React + Node.js

This document describes the migration from a Next.js monolith to separate React frontend and Node.js backend projects.

## Project Structure

```
vocab/
├── backend/                 # Node.js/Express backend
│   ├── src/
│   │   ├── server.ts       # Main server file
│   │   ├── routes/         # Express route handlers
│   │   ├── middleware/     # Express middleware
│   │   ├── backend/        # Business logic (migrated from original)
│   │   ├── config/         # Configuration
│   │   ├── models/         # Data models
│   │   └── types/          # TypeScript types
│   ├── prisma/             # Database schema and migrations
│   ├── package.json
│   └── .env
├── frontend/               # React frontend (Vite)
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # React contexts
│   │   ├── hooks/          # Custom hooks
│   │   ├── lib/            # Utilities and API client
│   │   ├── pages/          # Page components
│   │   ├── models/         # Data models
│   │   └── types/          # TypeScript types
│   ├── package.json
│   └── .env
└── README-MIGRATION.md     # This file
```

## Migration Changes

### Backend Changes
- **Framework**: Migrated from Next.js API routes to Express.js
- **Authentication**: Updated to use Express middleware instead of Next.js auth
- **API Structure**: Converted Next.js API routes to Express route handlers
- **CORS**: Added proper CORS configuration for frontend-backend communication
- **Middleware**: Added Express middleware for authentication, logging, and error handling

### Frontend Changes
- **Framework**: Migrated from Next.js to React with Vite
- **Routing**: Changed from Next.js App Router to React Router
- **API Calls**: Replaced Next.js API calls with HTTP client (Axios)
- **Authentication**: Updated to use token-based authentication with localStorage
- **Build Tool**: Changed from Next.js to Vite for faster development

## Setup Instructions

### Prerequisites
- Node.js 18+ 
- PostgreSQL database
- npm or yarn

### Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your database URL and other configuration
   ```

4. **Set up database**:
   ```bash
   npm run p:m  # Run Prisma migrations
   npm run p:g  # Generate Prisma client
   ```

5. **Start development server**:
   ```bash
   npm run dev
   ```

The backend will run on `http://localhost:3001`

### Frontend Setup

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env if needed (default backend URL is already set)
   ```

4. **Start development server**:
   ```bash
   npm run dev
   ```

The frontend will run on `http://localhost:5173`

## Development Workflow

### Running Both Projects

1. **Terminal 1 - Backend**:
   ```bash
   cd backend && npm run dev
   ```

2. **Terminal 2 - Frontend**:
   ```bash
   cd frontend && npm run dev
   ```

### API Communication

The frontend communicates with the backend via HTTP requests:
- **Base URL**: `http://localhost:3001/api`
- **Authentication**: JWT tokens stored in localStorage and sent as Bearer tokens
- **CORS**: Configured to allow requests from `http://localhost:5173`

### Available Scripts

#### Backend Scripts
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run p:m` - Run Prisma migrations
- `npm run p:g` - Generate Prisma client
- `npm run p:s` - Open Prisma Studio

#### Frontend Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## API Endpoints

### Authentication
- `POST /api/auth/login` - Login with username/password
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user
- `POST /api/auth/dev-login` - Development login (dev only)

### Collections
- `GET /api/collections` - Get all collections
- `GET /api/collections/:id` - Get collection by ID
- `POST /api/collections` - Create new collection
- `DELETE /api/collections/:id` - Delete collection
- `GET /api/collections/:id/stats` - Get collection statistics
- `POST /api/collections/:id/stats/track` - Track user activity

### Words
- `GET /api/words/search` - Search words
- `GET /api/words/:id` - Get word by ID
- `GET /api/words/:id/details` - Get word details
- `POST /api/words/:id/last-seen` - Save last seen word

### Other Endpoints
- `GET /api/keywords` - Get keywords
- `POST /api/feedback` - Submit feedback
- `POST /api/llm/*` - LLM/AI endpoints

## Environment Variables

### Backend (.env)
```env
DATABASE_URL=postgresql://username:password@localhost:5432/vocab_db
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:5173
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=86400
JWT_COOKIE_NAME=vocab-auth-token
OPENAI_API_KEY=your-openai-key
GOOGLE_AI_API_KEY=your-google-ai-key
```

### Frontend (.env)
```env
VITE_API_BASE_URL=http://localhost:3001
VITE_NODE_ENV=development
```

## Deployment

### Backend Deployment
1. Build the project: `npm run build`
2. Set production environment variables
3. Run migrations: `npm run p:m`
4. Start server: `npm start`

### Frontend Deployment
1. Build the project: `npm run build`
2. Serve the `dist` folder with a static file server
3. Update `VITE_API_BASE_URL` to point to production backend

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure backend CORS_ORIGIN matches frontend URL
2. **Authentication Issues**: Check JWT_SECRET is set and tokens are being sent
3. **Database Connection**: Verify DATABASE_URL is correct and database is running
4. **Port Conflicts**: Ensure ports 3001 (backend) and 5173 (frontend) are available

### Development Tips

1. **Hot Reload**: Both frontend and backend support hot reload in development
2. **API Testing**: Use the backend health endpoint `/health` to verify it's running
3. **Database**: Use Prisma Studio (`npm run p:s`) to inspect database data
4. **Logs**: Check console logs in both frontend and backend for debugging

## Migration Benefits

1. **Separation of Concerns**: Clear separation between frontend and backend
2. **Independent Deployment**: Frontend and backend can be deployed separately
3. **Technology Flexibility**: Can use different hosting solutions for each part
4. **Development Speed**: Vite provides faster development builds than Next.js
5. **Scalability**: Easier to scale frontend and backend independently
